<div>
    <!-- Loading Indicator for general actions -->
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('ui.loading-indicator', ['message' => 'Cargando productos...','type' => 'bar','color' => 'blue']);

$__html = app('livewire')->mount($__name, $__params, 'lw-3516708485-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    <!-- Search and Filters -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <!-- Search Component -->
            <div class="flex-1 max-w-md">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('products.product-search', ['initialSearch' => $search]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3516708485-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>

            <!-- Sort Component -->
            <div>
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('products.product-sort', ['initialSortBy' => $sortBy,'initialDirection' => $sortDirection]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3516708485-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <!-- Loading indicator for sorting -->
                
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="relative" 
        x-data="{ isLoading: false }"
        @products-loading-start.window="isLoading = true"
        @products-loading-end.window="setTimeout(() => isLoading = false, 1000)">
        <!-- Loading overlay for products grid -->
        <div x-show="isLoading" 
            x-transition
            class="absolute inset-0 bg-white bg-opacity-50 z-10 flex items-top justify-center">

            <div class="flex items-top justify-center space-x-2 py-4">
                <svg class="animate-spin h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-blue-600">Actualizando productos...</span>
            </div>
        </div>
        
        <!--[if BLOCK]><![endif]--><?php if($products->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('products.product-card', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, $product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                <?php echo e($products->links()); ?>

            </div>
        <?php else: ?>
            <!-- No Products Found -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2-2v7m14 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m14 0H6m14 0l-3-3m-3 3l-3-3"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No se encontraron productos</h3>
                <p class="mt-1 text-sm text-gray-500">
                    <!--[if BLOCK]><![endif]--><?php if($search): ?>
                        No hay productos que coincidan con "<?php echo e($search); ?>"
                    <?php else: ?>
                        No hay productos disponibles en este momento.
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
        <div x-data="{ show: true }" x-show="show" x-transition x-init="setTimeout(() => show = false, 3000)"
             class="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/products/product-catalog.blade.php ENDPATH**/ ?>
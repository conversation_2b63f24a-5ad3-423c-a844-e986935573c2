<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use ReflectionClass;
use ReflectionMethod;
use ReflectionProperty;
use ReflectionParameter;
use ReflectionException;

class GenerateClassReference extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:class-reference 
                            {--source-dir=. : Directorio origen para buscar archivos PHP}
                            {--output=clases_reference.md : Archivo de salida}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Genera documentación de clases PHP en formato Markdown';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sourceDir = $this->option('source-dir');
        $outputFile = $this->option('output');

        // Validar directorio origen
        if (!is_dir($sourceDir)) {
            $this->error("El directorio '{$sourceDir}' no existe.");
            return 1;
        }

        $this->info("Generando documentación de clases...");
        $this->info("Directorio origen: {$sourceDir}");
        $this->info("Archivo de salida: {$outputFile}");

        try {
            $classes = $this->scanForClasses($sourceDir);
            $this->generateMarkdown($classes, $outputFile);

            $this->info("Documentación generada exitosamente en: {$outputFile}");
            $this->info("Total de clases procesadas: " . count($classes));
        } catch (\Exception $e) {
            $this->error("Error al generar la documentación: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Escanea el directorio en busca de archivos PHP con clases
     */
    private function scanForClasses(string $sourceDir): array
    {
        $classes = [];
        $phpFiles = $this->findPhpFiles($sourceDir);

        $this->output->progressStart(count($phpFiles));

        foreach ($phpFiles as $file) {
            try {
                $fileClasses = $this->extractClassesFromFile($file);
                $classes = array_merge($classes, $fileClasses);
            } catch (\Exception $e) {
                $this->warn("Error procesando archivo {$file}: " . $e->getMessage());
            }
            $this->output->progressAdvance();
        }

        $this->output->progressFinish();

        return $classes;
    }

    /**
     * Encuentra todos los archivos PHP en el directorio RECURSIVAMENTE
     */
    private function findPhpFiles(string $directory): array
    {
        $phpFiles = [];

        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::SELF_FIRST
            );

            $this->info("Buscando archivos PHP recursivamente en: {$directory}");

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $phpFiles[] = $file->getPathname();
                    $relativePath = str_replace($directory . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $this->comment("  Encontrado: {$relativePath}");
                }
            }
        } catch (\Exception $e) {
            $this->error("Error al recorrer directorio recursivamente: " . $e->getMessage());
            return [];
        }

        sort($phpFiles);

        $this->info("Total archivos PHP encontrados recursivamente: " . count($phpFiles));

        // Mostrar estructura de directorios encontrada
        if (!empty($phpFiles)) {
            $directories = [];
            foreach ($phpFiles as $file) {
                $dir = dirname(str_replace($directory . DIRECTORY_SEPARATOR, '', $file));
                if ($dir !== '.' && !in_array($dir, $directories)) {
                    $directories[] = $dir;
                }
            }
            if (!empty($directories)) {
                $this->info("Subdirectorios con archivos PHP:");
                foreach ($directories as $dir) {
                    $this->comment("  - {$dir}");
                }
            }
        }

        return $phpFiles;
    }

    /**
     * Extrae información de las clases de un archivo PHP
     */
    private function extractClassesFromFile(string $filePath): array
    {
        $content = file_get_contents($filePath);
        $classes = [];

        // Incluir el archivo temporalmente para poder usar ReflectionClass
        $declaredClasses = get_declared_classes();
        $declaredInterfaces = get_declared_interfaces();
        $declaredTraits = get_declared_traits();

        // Intenta incluir el archivo de manera segura
        try {
            include_once $filePath;
        } catch (\Throwable $e) {
            // Si hay error al incluir, intentar parsear manualmente
            return $this->parseFileManually($content, $filePath);
        }

        $newClasses = array_diff(get_declared_classes(), $declaredClasses);
        $newInterfaces = array_diff(get_declared_interfaces(), $declaredInterfaces);
        $newTraits = array_diff(get_declared_traits(), $declaredTraits);

        $allNewTypes = array_merge($newClasses, $newInterfaces, $newTraits);

        foreach ($allNewTypes as $className) {
            try {
                $reflection = new ReflectionClass($className);
                if ($reflection->getFileName() === $filePath) {
                    $classes[] = $this->extractClassInfo($reflection);
                }
            } catch (ReflectionException $e) {
                continue;
            }
        }

        return $classes;
    }

    /**
     * Parsea manualmente un archivo PHP cuando no se puede incluir
     */
    private function parseFileManually(string $content, string $filePath): array
    {
        $classes = [];

        // Extraer namespace
        preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatches);
        $namespace = $namespaceMatches[1] ?? '';

        // Extraer clases, interfaces, enums y traits
        $patterns = [
            'class' => '/(?:abstract\s+)?class\s+(\w+)(?:\s+extends\s+(\w+))?(?:\s+implements\s+([^{]+))?/i',
            'interface' => '/interface\s+(\w+)(?:\s+extends\s+([^{]+))?/i',
            'trait' => '/trait\s+(\w+)/i',
            'enum' => '/enum\s+(\w+)(?:\s*:\s*(\w+))?/i'
        ];

        foreach ($patterns as $type => $pattern) {
            preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

            foreach ($matches as $match) {
                $className = $match[1];
                $fullClassName = $namespace ? $namespace . '\\' . $className : $className;

                $classInfo = [
                    'name' => $className,
                    'full_name' => $fullClassName,
                    'namespace' => $namespace,
                    'type' => $type,
                    'file' => $filePath,
                    'parent' => $match[2] ?? null,
                    'interfaces' => isset($match[3]) ? array_map('trim', explode(',', $match[3])) : [],
                    'properties' => [],
                    'methods' => [],
                    'doc_comment' => $this->extractDocComment($content, $match[0])
                ];

                $classes[] = $classInfo;
            }
        }

        return $classes;
    }

    /**
     * Extrae información completa de una clase usando ReflectionClass
     */
    private function extractClassInfo(ReflectionClass $reflection): array
    {
        $classInfo = [
            'name' => $reflection->getShortName(),
            'full_name' => $reflection->getName(),
            'namespace' => $reflection->getNamespaceName(),
            'type' => $this->getClassType($reflection),
            'file' => $reflection->getFileName(),
            'parent' => $reflection->getParentClass() ? $reflection->getParentClass()->getName() : null,
            'interfaces' => array_keys($reflection->getInterfaces()),
            'traits' => array_keys($reflection->getTraits()),
            'properties' => [],
            'methods' => [],
            'constants' => [],
            'doc_comment' => $this->cleanDocComment($reflection->getDocComment())
        ];

        // Extraer propiedades
        foreach ($reflection->getProperties() as $property) {
            if ($property->getDeclaringClass()->getName() === $reflection->getName()) {
                $classInfo['properties'][] = $this->extractPropertyInfo($property);
            }
        }

        // Extraer métodos
        foreach ($reflection->getMethods() as $method) {
            if ($method->getDeclaringClass()->getName() === $reflection->getName()) {
                $classInfo['methods'][] = $this->extractMethodInfo($method);
            }
        }

        // Extraer constantes
        foreach ($reflection->getReflectionConstants() as $constant) {
            if ($constant->getDeclaringClass()->getName() === $reflection->getName()) {
                $classInfo['constants'][] = [
                    'name' => $constant->getName(),
                    'value' => $constant->getValue(),
                    'visibility' => $this->getConstantVisibility($constant),
                    'doc_comment' => $this->cleanDocComment($constant->getDocComment())
                ];
            }
        }

        return $classInfo;
    }

    /**
     * Determina el tipo de clase
     */
    private function getClassType(ReflectionClass $reflection): string
    {
        if ($reflection->isInterface()) return 'interface';
        if ($reflection->isTrait()) return 'trait';
        if ($reflection->isEnum()) return 'enum';
        if ($reflection->isAbstract()) return 'abstract class';
        return 'class';
    }

    /**
     * Extrae información de una propiedad
     */
    private function extractPropertyInfo(ReflectionProperty $property): array
    {
        $type = null;
        if ($property->hasType()) {
            $type = $property->getType()->getName();
        }

        return [
            'name' => $property->getName(),
            'type' => $type,
            'visibility' => $this->getVisibility($property),
            'static' => $property->isStatic(),
            'default' => $property->hasDefaultValue() ? $property->getDefaultValue() : null,
            'doc_comment' => $this->cleanDocComment($property->getDocComment())
        ];
    }

    /**
     * Extrae información de un método
     */
    private function extractMethodInfo(ReflectionMethod $method): array
    {
        $parameters = [];
        foreach ($method->getParameters() as $param) {
            $parameters[] = $this->extractParameterInfo($param);
        }

        $returnType = null;
        if ($method->hasReturnType()) {
            $returnType = $method->getReturnType()->getName();
        }

        return [
            'name' => $method->getName(),
            'visibility' => $this->getVisibility($method),
            'static' => $method->isStatic(),
            'abstract' => $method->isAbstract(),
            'final' => $method->isFinal(),
            'return_type' => $returnType,
            'parameters' => $parameters,
            'doc_comment' => $this->cleanDocComment($method->getDocComment())
        ];
    }

    /**
     * Extrae información de un parámetro
     */
    private function extractParameterInfo(ReflectionParameter $param): array
    {
        $type = null;
        if ($param->hasType()) {
            $type = $param->getType()->getName();
        }

        $default = null;
        $hasDefault = false;
        try {
            if ($param->isDefaultValueAvailable()) {
                $default = $param->getDefaultValue();
                $hasDefault = true;
            }
        } catch (\ReflectionException $e) {
            // Ignorar errores al obtener valor por defecto
        }

        return [
            'name' => $param->getName(),
            'type' => $type,
            'optional' => $param->isOptional(),
            'has_default' => $hasDefault,
            'default' => $default,
            'by_reference' => $param->isPassedByReference(),
            'variadic' => $param->isVariadic()
        ];
    }

    /**
     * Obtiene la visibilidad de una propiedad o método
     */
    private function getVisibility($reflection): string
    {
        if ($reflection->isPublic()) return 'public';
        if ($reflection->isProtected()) return 'protected';
        if ($reflection->isPrivate()) return 'private';
        return 'public';
    }

    /**
     * Obtiene la visibilidad de una constante
     */
    private function getConstantVisibility($constant): string
    {
        if (method_exists($constant, 'isPublic')) {
            if ($constant->isPublic()) return 'public';
            if ($constant->isProtected()) return 'protected';
            if ($constant->isPrivate()) return 'private';
        }
        return 'public';
    }

    /**
     * Limpia los comentarios de documentación
     */
    private function cleanDocComment($docComment): ?string
    {
        if (!$docComment) return null;

        $lines = explode("\n", $docComment);
        $cleaned = [];

        foreach ($lines as $line) {
            $line = trim($line);
            $line = ltrim($line, '/*');
            $line = rtrim($line, '*/');
            $line = ltrim($line, '*');
            $line = trim($line);

            if (!empty($line)) {
                $cleaned[] = $line;
            }
        }

        return implode("\n", $cleaned);
    }

    /**
     * Extrae comentario de documentación manualmente
     */
    private function extractDocCommentManual(string $content, int $classPosition): ?string
    {
        // Buscar el último comentario /** antes de la posición de la clase
        $beforeClass = substr($content, 0, $classPosition);

        if (preg_match_all('/\/\*\*(.*?)\*\//s', $beforeClass, $matches, PREG_OFFSET_CAPTURE)) {
            $lastMatch = end($matches[0]);
            return $this->cleanDocComment($lastMatch[0]);
        }

        return null;
    }

    /**
     * Parsea propiedades manualmente
     */
    private function parseProperties(string $content): array
    {
        $properties = [];
        $pattern = '/(?:(public|private|protected)\s+)(?:(static)\s+)?(?:(\w+)\s+)?\$(\w+)(?:\s*=\s*([^;]+))?;/';

        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $properties[] = [
                    'name' => $match[4],
                    'visibility' => $match[1] ?? 'public',
                    'static' => !empty($match[2]),
                    'type' => $match[3] ?? null,
                    'default' => isset($match[5]) ? trim($match[5]) : null,
                    'doc_comment' => null
                ];
            }
        }

        return $properties;
    }

    /**
     * Parsea métodos manualmente
     */
    private function parseMethods(string $content): array
    {
        $methods = [];
        $pattern = '/(?:(public|private|protected)\s+)?(?:(static|abstract|final)\s+)?function\s+(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?/';

        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $parameters = [];
                if (!empty($match[4])) {
                    $paramList = array_map('trim', explode(',', $match[4]));
                    foreach ($paramList as $param) {
                        if (preg_match('/(?:(\w+)\s+)?(?:(&))?(?:(\.\.\.))?\$(\w+)(?:\s*=\s*([^,]+))?/', $param, $paramMatch)) {
                            $parameters[] = [
                                'name' => $paramMatch[4],
                                'type' => $paramMatch[1] ?? null,
                                'by_reference' => !empty($paramMatch[2]),
                                'variadic' => !empty($paramMatch[3]),
                                'default' => isset($paramMatch[5]) ? trim($paramMatch[5]) : null,
                                'has_default' => isset($paramMatch[5]),
                                'optional' => isset($paramMatch[5])
                            ];
                        }
                    }
                }

                $methods[] = [
                    'name' => $match[3],
                    'visibility' => $match[1] ?? 'public',
                    'static' => str_contains($match[2] ?? '', 'static'),
                    'abstract' => str_contains($match[2] ?? '', 'abstract'),
                    'final' => str_contains($match[2] ?? '', 'final'),
                    'return_type' => isset($match[5]) ? trim($match[5]) : null,
                    'parameters' => $parameters,
                    'doc_comment' => null
                ];
            }
        }

        return $methods;
    }

    /**
     * Parsea constantes manualmente
     */
    private function parseConstants(string $content): array
    {
        $constants = [];
        $pattern = '/(?:(public|private|protected)\s+)?const\s+(\w+)\s*=\s*([^;]+);/';

        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $constants[] = [
                    'name' => $match[2],
                    'visibility' => $match[1] ?? 'public',
                    'value' => trim($match[3]),
                    'doc_comment' => null
                ];
            }
        }

        return $constants;
    }

    /**
     * Genera el archivo Markdown con la documentación
     */
    private function generateMarkdown(array $classes, string $outputFile): void
    {
        // Agrupar clases por namespace
        $groupedClasses = [];
        foreach ($classes as $class) {
            $namespace = $class['namespace'] ?: 'Global';
            $groupedClasses[$namespace][] = $class;
        }

        ksort($groupedClasses);

        $markdown = $this->generateHeader();

        foreach ($groupedClasses as $namespace => $namespaceClasses) {
            $markdown .= $this->generateNamespaceSection($namespace, $namespaceClasses);
        }

        file_put_contents($outputFile, $markdown);
    }

    /**
     * Genera el encabezado del archivo
     */
    private function generateHeader(): string
    {
        $date = date('Y-m-d H:i:s');
        return <<<MD
# Referencia de Clases

*Generado automáticamente el {$date}*

Este documento contiene la documentación de todas las clases, interfaces, traits y enums del proyecto.

---

MD;
    }

    /**
     * Genera una sección para un namespace
     */
    private function generateNamespaceSection(string $namespace, array $classes): string
    {
        $markdown = "\n## Namespace: `{$namespace}`\n\n";

        // Ordenar clases por nombre
        usort($classes, function ($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        foreach ($classes as $class) {
            $markdown .= $this->generateClassSection($class);
        }

        return $markdown;
    }

    /**
     * Genera una sección para una clase
     */
    private function generateClassSection(array $class): string
    {
        $markdown = "\n### {$class['type']}: `{$class['name']}`\n\n";

        // Información básica
        $markdown .= "**Nombre completo:** `{$class['full_name']}`\n\n";
        $markdown .= "**Archivo:** `{$class['file']}`\n\n";

        if ($class['parent']) {
            $markdown .= "**Extiende:** `{$class['parent']}`\n\n";
        }

        if (!empty($class['interfaces'])) {
            $interfaces = implode('`, `', $class['interfaces']);
            $markdown .= "**Implementa:** `{$interfaces}`\n\n";
        }

        if (!empty($class['traits'])) {
            $traits = implode('`, `', $class['traits']);
            $markdown .= "**Usa traits:** `{$traits}`\n\n";
        }

        // Documentación
        if ($class['doc_comment']) {
            $markdown .= "**Descripción:**\n```\n{$class['doc_comment']}\n```\n\n";
        }

        // Constantes
        if (!empty($class['constants'])) {
            $markdown .= "#### Constantes\n\n";
            foreach ($class['constants'] as $constant) {
                $value = is_string($constant['value']) ? "'{$constant['value']}'" : var_export($constant['value'], true);
                $markdown .= "- `{$constant['visibility']} const {$constant['name']} = {$value}`";
                if ($constant['doc_comment']) {
                    $markdown .= "\n  ```\n  {$constant['doc_comment']}\n  ```";
                }
                $markdown .= "\n\n";
            }
        }

        // Propiedades
        if (!empty($class['properties'])) {
            $markdown .= "#### Propiedades\n\n";
            foreach ($class['properties'] as $property) {
                $static = $property['static'] ? 'static ' : '';
                $type = $property['type'] ? "{$property['type']} " : '';
                $default = $property['default'] !== null ? " = " . var_export($property['default'], true) : '';

                $markdown .= "- `{$property['visibility']} {$static}{$type}\${$property['name']}{$default}`";
                if ($property['doc_comment']) {
                    $markdown .= "\n  ```\n  {$property['doc_comment']}\n  ```";
                }
                $markdown .= "\n\n";
            }
        }

        // Métodos
        if (!empty($class['methods'])) {
            $markdown .= "#### Métodos\n\n";
            foreach ($class['methods'] as $method) {
                $markdown .= $this->generateMethodSignature($method);
            }
        }

        $markdown .= "---\n\n";
        return $markdown;
    }

    /**
     * Genera la firma de un método
     */
    private function generateMethodSignature(array $method): string
    {
        $modifiers = [];
        $modifiers[] = $method['visibility'];

        if ($method['static']) $modifiers[] = 'static';
        if ($method['abstract']) $modifiers[] = 'abstract';
        if ($method['final']) $modifiers[] = 'final';

        $parameters = [];
        foreach ($method['parameters'] as $param) {
            $paramStr = '';
            if ($param['type']) $paramStr .= $param['type'] . ' ';
            if ($param['by_reference']) $paramStr .= '&';
            if ($param['variadic']) $paramStr .= '...';
            $paramStr .= '$' . $param['name'];

            if ($param['has_default']) {
                $defaultValue = var_export($param['default'], true);
                $paramStr .= ' = ' . $defaultValue;
            }

            $parameters[] = $paramStr;
        }

        $parameterList = implode(', ', $parameters);
        $returnType = $method['return_type'] ? ': ' . $method['return_type'] : '';
        $modifierStr = implode(' ', $modifiers);

        $signature = "`{$modifierStr} function {$method['name']}({$parameterList}){$returnType}`";

        $markdown = "##### {$signature}\n\n";

        if ($method['doc_comment']) {
            $markdown .= "```\n{$method['doc_comment']}\n```\n\n";
        }

        return $markdown;
    }
}

<div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md mx-auto">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        🧪 Test de Localización Livewire
    </h3>

    <!-- Información del locale actual -->
    <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
        <p class="text-sm text-blue-800 dark:text-blue-200">
            <strong>Locale actual:</strong> <?php echo e($currentLocale); ?>

        </p>
        <p class="text-sm text-blue-800 dark:text-blue-200">
            <strong>URL actual:</strong> <?php echo e(request()->url()); ?>

        </p>
    </div>

    <!-- Contador -->
    <div class="text-center mb-4">
        <div class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            <?php echo e($counter); ?>

        </div>

        <div class="flex justify-center space-x-2 mb-3">
            <button
                wire:click="decrement"
                class="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded transition-colors"
                wire:loading.attr="disabled"
            >
                -
            </button>

            <button
                wire:click="increment"
                class="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded transition-colors"
                wire:loading.attr="disabled"
            >
                +
            </button>

            <button
                wire:click="resetCounter"
                class="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded transition-colors"
                wire:loading.attr="disabled"
            >
                Reset
            </button>
        </div>
    </div>

    <!-- Test AJAX -->
    <div class="mb-4">
        <button
            wire:click="testAjax"
            class="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
            wire:loading.attr="disabled"
        >
            <span wire:loading.remove>🔄 Test AJAX</span>
            <span wire:loading>⏳ Cargando...</span>
        </button>
    </div>

    <!-- Mensaje de estado -->
    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded">
        <p class="text-sm text-gray-700 dark:text-gray-300">
            <strong>Estado:</strong> <?php echo e($message); ?>

        </p>
        
    </div>

    <!-- Indicador de carga global -->
    <div wire:loading class="mt-3 text-center">
        <div class="inline-flex items-center px-3 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 rounded-full text-sm">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Procesando...
        </div>
    </div>
</div>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/test/localization-test.blade.php ENDPATH**/ ?>
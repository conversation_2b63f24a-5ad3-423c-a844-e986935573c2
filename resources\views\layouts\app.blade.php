<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Quantum Transition') }}</title>

    {{-- Carga de CSS y JS con Vite (compila Tailwind y tu JS personalizado) --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    {{-- Livewire Styles --}}
    @livewireStyles
</head>
<body class="antialiased bg-primary text-gray-900">

    {{-- Contenido dinámico desde otras vistas --}}
    @yield('content')

    {{-- Livewire Scripts --}}
    @livewireScripts
</body>
</html>

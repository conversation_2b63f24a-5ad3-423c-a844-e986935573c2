<?php

namespace App\Livewire\Ui;

use Livewire\Component;

class LoadingIndicator extends Component
{
    public $target = '';
    public $message = 'Cargando...';
    public $type = 'spinner'; // spinner, dots, bar, overlay
    public $size = 'md'; // sm, md, lg
    public $color = 'blue'; // blue, gray, green, red

    public function mount($target = '', $message = 'Cargando...', $type = 'spinner', $size = 'md', $color = 'blue')
    {
        $this->target = $target;
        $this->message = $message;
        $this->type = $type;
        $this->size = $size;
        $this->color = $color;
    }

    public function render()
    {
        return view('livewire.ui.loading-indicator');
    }
}

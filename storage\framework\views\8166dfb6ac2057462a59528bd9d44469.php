<div class="flex items-center space-x-4">
    <!-- Contenedor fijo para spinner (izquierda) -->
    <div class="flex-none w-5 h-5 flex items-center justify-center">
        <div wire:loading wire:target="sortBy,sortDirection,toggleDirection"
             class="animate-spin text-blue-500">
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"
                 fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10"
                        stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"/>
            </svg>
        </div>
    </div>

    <!-- Resto del componente -->
    <label class="text-sm font-medium text-gray-700">Ordenar por:</label>

    <select wire:model.live="sortBy" 
            class="border border-gray-300 rounded-md px-3 py-2 text-sm">
        <option value="name">Nombre</option>
        <option value="price">Precio</option>
        <option value="created_at">Fecha</option>
    </select>

    <button wire:click="toggleDirection"
            class="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200">
        <!--[if BLOCK]><![endif]--><?php if($sortDirection === 'asc'): ?>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"></path>
            </svg>
        <?php else: ?>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"></path>
            </svg>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </button>
</div>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/products/product-sort.blade.php ENDPATH**/ ?>
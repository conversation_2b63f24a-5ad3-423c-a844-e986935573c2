<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Debug Livewire</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

</head>
<body class="antialiased bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Debug Livewire + LaravelLocalization</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Información del entorno -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Información del Entorno</h2>
                <ul class="space-y-2 text-sm">
                    <li><strong>URL actual:</strong> <?php echo e(request()->url()); ?></li>
                    <li><strong>Ruta actual:</strong> <?php echo e(request()->path()); ?></li>
                    <li><strong>Locale actual:</strong> <?php echo e(app()->getLocale()); ?></li>
                    <li><strong>Livewire registrado:</strong> <?php echo e(app()->bound('livewire') ? 'Sí' : 'No'); ?></li>
                    <li><strong>Ruta de update:</strong> /livewire/update</li>
                    <li><strong>CSRF Token:</strong> <?php echo e(csrf_token()); ?></li>
                    <li><strong>Session ID:</strong> <?php echo e(session()->getId()); ?></li>
                </ul>
            </div>

            <!-- Scripts de Livewire -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Scripts de Livewire</h2>
                <div class="text-sm">
                    <p class="mb-2"><strong>Livewire JS URL:</strong></p>
                    <code class="bg-gray-100 p-2 rounded block">/livewire/livewire.js</code>
                    
                    <p class="mt-4 mb-2"><strong>Update URL:</strong></p>
                    <code class="bg-gray-100 p-2 rounded block">/livewire/update</code>
                </div>
            </div>
        </div>

        <!-- Componente de prueba -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Componente de Prueba</h2>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('test.localization-test', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-850960376-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>

        <!-- JavaScript Debug -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Debug JavaScript</h2>
            <div id="debug-info" class="text-sm bg-gray-100 p-4 rounded">
                <p>Cargando información de debug...</p>
            </div>
        </div>
    </div>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const debugInfo = document.getElementById('debug-info');
            
            let info = [];
            info.push('Livewire cargado: ' + (typeof window.Livewire !== 'undefined' ? 'Sí' : 'No'));
            
            if (typeof window.Livewire !== 'undefined') {
                info.push('Livewire versión: ' + (window.Livewire.version || 'Desconocida'));
                info.push('Componentes registrados: ' + Object.keys(window.Livewire.components || {}).length);
            }
            
            info.push('Alpine cargado: ' + (typeof window.Alpine !== 'undefined' ? 'Sí' : 'No'));
            info.push('URL base: ' + window.location.origin);
            info.push('Ruta actual: ' + window.location.pathname);
            
            debugInfo.innerHTML = info.map(item => '<p>' + item + '</p>').join('');
        });

        // Interceptar peticiones AJAX para debug
        if (typeof window.Livewire !== 'undefined') {
            document.addEventListener('livewire:init', () => {
                console.log('Livewire inicializado correctamente');
            });

            document.addEventListener('livewire:request', (event) => {
                console.log('Petición Livewire enviada:', event.detail);
            });

            document.addEventListener('livewire:response', (event) => {
                console.log('Respuesta Livewire recibida:', event.detail);
            });

            document.addEventListener('livewire:error', (event) => {
                console.error('Error Livewire:', event.detail);
            });
        }
    </script>
</body>
</html>
<?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/debug-livewire.blade.php ENDPATH**/ ?>
<div>
    <label for="search" class="sr-only">Buscar productos</label>

    <div class="flex items-center gap-2">
        <!-- Contenedor input -->
        <div class="relative flex-1">
            <!-- Icono lupa -->
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>

            <!-- Input -->
            <input wire:model.live.debounce.300ms="search"
                   type="text"
                   id="search"
                   class="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5
                          bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400
                          focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Buscar productos...">

            <!-- Bot<PERSON> limpiar -->
            @if($search)
                <button wire:click="clearSearch"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none"
                         viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            @endif
        </div>

        <!-- Contenedor fijo para spinner (reserva espacio) -->
        <div class="flex-none w-5 h-5 flex items-center justify-center">
            <div wire:loading wire:target="search" class="animate-spin text-blue-500">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"
                     fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10"
                            stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                          d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"/>
                </svg>
            </div>
        </div>
    </div>
</div>

<?php

namespace App\Livewire\Products;

use Livewire\Component;

class ProductSort extends Component
{
    public $sortBy = 'name';
    public $sortDirection = 'asc';

    public function mount($initialSortBy = 'name', $initialDirection = 'asc')
    {
        $this->sortBy = $initialSortBy;
        $this->sortDirection = $initialDirection;
    }

    public function updatedSortBy()
    {
        $this->dispatch('products-loading-start');
        $this->dispatch('sortUpdated', $this->sortBy, $this->sortDirection);
        $this->dispatch('products-loading-end');
    }

    public function toggleDirection()
    {
        $this->dispatch('products-loading-start');
        $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->dispatch('sortUpdated', $this->sortBy, $this->sortDirection);
        $this->dispatch('products-loading-end');
    }

    public function render()
    {
        return view('livewire.products.product-sort');
    }
}

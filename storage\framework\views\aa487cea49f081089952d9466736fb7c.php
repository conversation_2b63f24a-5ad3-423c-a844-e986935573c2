<!--[if BLOCK]><![endif]--><?php if($products->count() > 0): ?>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('products.product-card', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, $product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
<?php else: ?>
    <!-- No Products Found -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2-2v7m14 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m14 0H6m14 0l-3-3m-3 3l-3-3"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No se encontraron productos</h3>
        <p class="mt-1 text-sm text-gray-500">
            <!--[if BLOCK]><![endif]--><?php if($search): ?>
                No hay productos que coincidan con "<?php echo e($search); ?>"
            <?php else: ?>
                No hay productos disponibles en este momento.
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </p>
    </div>
<?php endif; ?><!--[if ENDBLOCK]><![endif]--><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/livewire/products/product-grid.blade.php ENDPATH**/ ?>
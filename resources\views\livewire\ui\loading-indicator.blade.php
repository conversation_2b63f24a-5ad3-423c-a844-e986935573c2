@php
    $sizeClasses = [
        'sm' => 'h-4 w-4',
        'md' => 'h-6 w-6', 
        'lg' => 'h-8 w-8'
    ];
    
    $colorClasses = [
        'blue' => 'text-blue-600',
        'gray' => 'text-gray-600',
        'green' => 'text-green-600',
        'red' => 'text-red-600'
    ];
    
    $spinnerSize = $sizeClasses[$size] ?? $sizeClasses['md'];
    $spinnerColor = $colorClasses[$color] ?? $colorClasses['blue'];
@endphp

<div wire:loading{{ $target ? ".target={$target}" : '' }} class="loading-indicator">
    @if($type === 'overlay')
        <!-- Overlay Loading -->
        <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 flex items-center space-x-3 shadow-xl">
                <svg class="animate-spin {{ $spinnerSize }} {{ $spinnerColor }}" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-gray-700 font-medium">{{ $message }}</span>
            </div>
        </div>
    
    @elseif($type === 'bar')
        <!-- Progress Bar Loading -->
        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div class="bg-{{ $color }}-600 h-2 rounded-full loading-bar"></div>
        </div>
        
    @elseif($type === 'dots')
        <!-- Dots Loading -->
        <div class="flex items-center justify-center space-x-2">
            <div class="flex space-x-1">
                <div class="w-2 h-2 {{ str_replace('text-', 'bg-', $spinnerColor) }} rounded-full animate-bounce"></div>
                <div class="w-2 h-2 {{ str_replace('text-', 'bg-', $spinnerColor) }} rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 {{ str_replace('text-', 'bg-', $spinnerColor) }} rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            <span class="text-sm {{ $spinnerColor }} ml-2">{{ $message }}</span>
        </div>
        
    @else
        <!-- Default Spinner Loading -->
        <div class="flex items-center justify-center space-x-2 py-4">
            <svg class="animate-spin {{ $spinnerSize }} {{ $spinnerColor }}" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm {{ $spinnerColor }}">{{ $message }}</span>
        </div>
    @endif

    <style>
        .loading-bar {
            animation: loading 2s infinite;
        }
        
        @keyframes loading {
            0% {
                width: 0%;
            }
            50% {
                width: 100%;
            }
            100% {
                width: 0%;
            }
        }
    </style>
</div>


[2025-09-11 16:13:07] local.ERROR: Call to undefined method ReflectionUnionType::getName() {"exception":"[object] (Error(code: 0): Call to undefined method ReflectionUnionType::getName() at C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Console\\Commands\\GenerateClassReference.php:441)
[stacktrace]
#0 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Console\\Commands\\GenerateClassReference.php(414): App\\Console\\Commands\\GenerateClassReference->extractParameterInfo(Object(ReflectionParameter))
#1 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Console\\Commands\\GenerateClassReference.php(356): App\\Console\\Commands\\GenerateClassReference->extractMethodInfo(Object(ReflectionMethod))
#2 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Console\\Commands\\GenerateClassReference.php(171): App\\Console\\Commands\\GenerateClassReference->extractClassInfo(Object(ReflectionClass))
#3 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Console\\Commands\\GenerateClassReference.php(76): App\\Console\\Commands\\GenerateClassReference->extractClassesFromFile('.\\\\vendor\\\\lbcdev...')
#4 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\app\\Console\\Commands\\GenerateClassReference.php(51): App\\Console\\Commands\\GenerateClassReference->scanForClasses('.\\\\vendor\\\\lbcdev...')
#5 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\GenerateClassReference->handle()
#6 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateClassReference), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Proyectos\\Clientes\\Transition\\Quantum\\quantum_webapp\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 

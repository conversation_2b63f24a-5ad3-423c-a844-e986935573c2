<?php

namespace App\Livewire\Test;

use Livewire\Component;

class LocalizationTest extends Component
{
    public $counter = 0;
    public $message = '';
    public $currentLocale = '';

    public function mount()
    {
        $this->currentLocale = app()->getLocale();
        $this->message = 'Componente cargado correctamente';
    }

    public function increment()
    {
        $this->counter++;
        $this->message = "Contador incrementado a {$this->counter}";
    }

    public function decrement()
    {
        $this->counter--;
        $this->message = "Contador decrementado a {$this->counter}";
    }

    public function resetCounter()
    {
        $this->counter = 0;
        $this->message = 'Contador reiniciado';
    }

    public function testAjax()
    {
        $this->message = 'Petición AJAX funcionando correctamente - ' . now()->format('H:i:s');
    }

    public function render()
    {
        return view('livewire.test.localization-test');
    }
}
